#include "ReportingLogic.h"

ReportingLogic::ReportingLogic(SensorDataManager& dataManager, ConnectivityManager& connectivity) :
    dataManager(dataManager),
    connectivity(connectivity),
    initialized(false),
    totalReports(0),
    thresholdReports(0),
    dailyReports(0),
    errorReports(0) {
}

void ReportingLogic::begin() {
    initialized = true;
    DEBUG_PRINTLN("ReportingLogic initialized");
}

ReportDecision ReportingLogic::analyzeReading(const SensorReading& currentReading) {
    ReportDecision decision;
    
    if (!currentReading.valid) {
        decision.shouldReport = true;
        decision.reasons |= REPORT_ERROR;
        decision.reasonText = "Invalid sensor reading";
        return decision;
    }
    
    // Check if this is the first reading
    SensorReading lastReading = dataManager.getLastReading();
    if (!lastReading.valid) {
        decision.shouldReport = true;
        decision.reasons |= REPORT_FIRST_READING;
    }
    
    // Check threshold-based reporting
    if (dataManager.shouldReportByThreshold(currentReading)) {
        decision.shouldReport = true;
        
        if (lastReading.valid) {
            int tdsDiff = abs(currentReading.tds - lastReading.tds);
            float tempDiff = abs(currentReading.temperature - lastReading.temperature);
            
            if (tdsDiff >= 1000) {
                decision.reasons |= REPORT_TDS_THRESHOLD;
            }
            if (tempDiff >= 1.0) {
                decision.reasons |= REPORT_TEMP_THRESHOLD;
            }
        }
    }
    
    // Check daily reporting
    if (dataManager.shouldReportDaily()) {
        decision.shouldReport = true;
        decision.reasons |= REPORT_DAILY;
    }
    
    // Build reason text
    decision.reasonText = buildReasonText(decision.reasons);
    
    return decision;
}

bool ReportingLogic::executeReport(const SensorReading& reading, const ReportDecision& decision) {
    DEBUG_PRINTF("Executing report: %s\n", decision.reasonText.c_str());
    
    // Attempt to connect if not already connected
    if (!attemptConnection()) {
        DEBUG_PRINTLN("Failed to establish connection for reporting");
        logReportAttempt(decision, false);
        return false;
    }
    
    // Determine if this is a daily report
    bool isDailyReport = (decision.reasons & REPORT_DAILY) != 0;

    // Get next daily report time
    time_t nextDailyReportTime = dataManager.getNextDailyReportTimestamp();

    // Publish sensor data
    bool success = connectivity.publishSensorData(reading, isDailyReport, nextDailyReportTime);
    
    if (success) {
        // Update reporting state
        dataManager.updateReportState(isDailyReport);
        
        // Update statistics
        totalReports++;
        if (decision.reasons & REPORT_DAILY) dailyReports++;
        if (decision.reasons & (REPORT_TDS_THRESHOLD | REPORT_TEMP_THRESHOLD)) thresholdReports++;
        if (decision.reasons & REPORT_ERROR) errorReports++;
        
        DEBUG_PRINTF("Report successful! Total reports: %u\n", totalReports);
    }
    
    logReportAttempt(decision, success);
    return success;
}

bool ReportingLogic::isDailyReportDue() {
    return dataManager.shouldReportDaily();
}

bool ReportingLogic::forceReport(const SensorReading& reading, const char* reason) {
    ReportDecision decision;
    decision.shouldReport = true;
    decision.reasons = REPORT_NONE; // Custom reason
    decision.reasonText = String("Forced: ") + reason;
    
    return executeReport(reading, decision);
}

void ReportingLogic::printReportingStats() {
    DEBUG_PRINTLN("=== Reporting Statistics ===");
    DEBUG_PRINTF("Total reports: %u\n", totalReports);
    DEBUG_PRINTF("Threshold reports: %u\n", thresholdReports);
    DEBUG_PRINTF("Daily reports: %u\n", dailyReports);
    DEBUG_PRINTF("Error reports: %u\n", errorReports);
    
    ReportingState state = dataManager.getReportingState();
    DEBUG_PRINTF("Boot count: %u\n", state.bootCount);
    DEBUG_PRINTF("Last report time: %lu\n", state.lastReportTime);
    DEBUG_PRINTF("Next daily report: %lu\n", state.nextDailyReportTime);
    DEBUG_PRINTLN("============================");
}

String ReportingLogic::buildReasonText(uint8_t reasons) {
    if (reasons == REPORT_NONE) {
        return "No report needed";
    }
    
    String text = "";
    bool first = true;
    
    if (reasons & REPORT_FIRST_READING) {
        if (!first) text += ", ";
        text += "First reading";
        first = false;
    }
    
    if (reasons & REPORT_TDS_THRESHOLD) {
        if (!first) text += ", ";
        text += "TDS threshold exceeded";
        first = false;
    }
    
    if (reasons & REPORT_TEMP_THRESHOLD) {
        if (!first) text += ", ";
        text += "Temperature threshold exceeded";
        first = false;
    }
    
    if (reasons & REPORT_DAILY) {
        if (!first) text += ", ";
        text += "Daily report due";
        first = false;
    }
    
    if (reasons & REPORT_ERROR) {
        if (!first) text += ", ";
        text += "Error condition";
        first = false;
    }
    
    return text;
}

bool ReportingLogic::attemptConnection() {
    // Check if already connected
    if (connectivity.isConnected()) {
        return true;
    }
    
    DEBUG_PRINTLN("Attempting to establish connection...");
    
    // Try to connect to WiFi
    if (!connectivity.connectWiFi(WIFI_TIMEOUT_MS)) {
        DEBUG_PRINTLN("WiFi connection failed");
        return false;
    }

    // Try to connect to MQTT
    if (!connectivity.connectMQTT(MQTT_TIMEOUT_MS)) {
        DEBUG_PRINTLN("MQTT connection failed");
        return false;
    }
    
    DEBUG_PRINTLN("Connection established successfully");
    return true;
}

void ReportingLogic::logReportAttempt(const ReportDecision& decision, bool success) {
    DEBUG_PRINTF("Report attempt - Reason: %s, Success: %s\n", 
                  decision.reasonText.c_str(), 
                  success ? "YES" : "NO");
    
    if (!success) {
        DEBUG_PRINTF("Connection status: %s\n", connectivity.getConnectionStatus().c_str());
    }
}
