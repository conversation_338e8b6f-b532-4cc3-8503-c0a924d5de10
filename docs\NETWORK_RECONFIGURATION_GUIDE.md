# 网络重配置方案设计

## 📋 方案概述

设计了一个基于连续重启检测的网络重配置方案，当前使用reset按钮，后续可扩展支持专用按钮和LED指示。

## 🔧 当前方案：连续重启检测

### 工作原理
1. **重启计数**: 系统记录30秒内的连续重启次数
2. **触发条件**: 30秒内连续重启3次自动触发重配网
3. **自动清除**: 成功连接WiFi后自动清除重启计数器

### 使用方法
```
用户操作：快速按reset按钮3次（每次间隔<30秒）
系统响应：自动清除WiFi凭据，启动BluFi配网模式
```

## 🎯 配置参数

### Config.h 配置项
```cpp
// Network Reconfiguration
#define RESET_COUNT_THRESHOLD 3        // 触发重配网的重启次数
#define RESET_TIME_WINDOW_SECONDS 30   // 重启计数时间窗口（秒）
#define RECONFIG_BUTTON_PIN -1         // 专用按钮GPIO（-1=未使用）
#define RECONFIG_LED_PIN -1            // 状态LED GPIO（-1=未使用）
#define RECONFIG_BUTTON_HOLD_TIME_MS 5000  // 按钮长按时间（毫秒）
```

### 可调整参数
- **重启次数阈值**: 1-10次（推荐3次）
- **时间窗口**: 10-60秒（推荐30秒）
- **按钮长按时间**: 3-10秒（推荐5秒）

## 🔄 工作流程

### 1. 系统启动流程
```
1. 系统启动
2. 初始化NetworkReconfigManager
3. 记录当前重启
4. 检查是否满足重配网条件
5. 如果满足：清除WiFi凭据 → 启动BluFi
6. 如果不满足：正常启动流程
```

### 2. 重配网触发流程
```
用户快速重启3次
↓
系统检测到重启次数≥3且在30秒内
↓
自动触发重配网模式
↓
清除存储的WiFi凭据
↓
启动BluFi配网（3分钟超时）
↓
配网成功：清除重启计数器
配网失败：保持重启计数器
```

### 3. 正常连接流程
```
系统启动
↓
检查WiFi凭据
↓
尝试连接WiFi
↓
连接成功：清除重启计数器
连接失败：保持重启计数器
```

## 📊 调试信息

### 启动时的调试输出
```
NetworkReconfigManager initialized. Reset count: 2
Starting new reset sequence
Reset count incremented to: 3
Reconfiguration triggered by 3 resets within 30 seconds
=== NETWORK RECONFIGURATION TRIGGERED ===
Starting BluFi provisioning for reconfiguration...
```

### 状态查询
```cpp
// 获取当前重启次数
uint8_t count = reconfigManager.getResetCount();

// 获取最后重启时间
uint32_t lastTime = reconfigManager.getLastResetTime();

// 强制触发重配网（调试用）
reconfigManager.forceReconfig();
```

## 🚀 未来扩展：硬件按钮+LED

### 硬件连接
```cpp
// 在Config.h中配置GPIO
#define RECONFIG_BUTTON_PIN 0    // 按钮连接到GPIO0
#define RECONFIG_LED_PIN 2       // LED连接到GPIO2
```

### 按钮操作
```
长按按钮5秒 → LED开始闪烁 → 触发重配网
```

### LED状态指示
- **常灭**: 正常工作状态
- **闪烁**: 重配网模式激活
- **常亮**: 配网过程中（可选）

## 🔧 实现细节

### NetworkReconfigManager类
```cpp
class NetworkReconfigManager {
public:
    void begin();                    // 初始化
    bool shouldTriggerReconfig();    // 检查是否应触发重配网
    void recordReset();              // 记录重启
    void clearResetCounter();        // 清除重启计数器
    bool checkButtonPress();         // 检查按钮状态
    void updateLED(bool blinking);   // 更新LED状态
};
```

### NVS存储
```cpp
// 存储键名
"net_reconfig/reset_count"      // 重启次数
"net_reconfig/last_reset"       // 最后重启时间
"net_reconfig/first_reset"      // 首次重启时间
```

## ⚠️ 注意事项

### 1. 时间窗口机制
- 只有在时间窗口内的连续重启才会累计
- 超出时间窗口会重新开始计数
- 避免意外触发重配网

### 2. 自动清除机制
- WiFi连接成功后自动清除计数器
- 防止正常使用时误触发
- 确保重配网功能的可靠性

### 3. 调试建议
- 开启串口调试查看重启计数
- 测试时注意时间窗口限制
- 验证WiFi连接成功后计数器清除

### 4. 生产环境
- 可以调整重启次数阈值（如5次）
- 可以延长时间窗口（如60秒）
- 建议添加硬件按钮作为备用方案

## 📱 BluFi配网流程

### 1. 手机端操作
```
1. 下载ESP BluFi App
2. 搜索设备（fish-tank-sensor-1-xxxx）
3. 连接设备
4. 输入WiFi SSID和密码
5. 等待配网完成
```

### 2. 设备端状态
```
1. 启动BluFi广播
2. 等待手机连接
3. 接收WiFi凭据
4. 尝试连接WiFi
5. 连接成功：保存凭据，清除重启计数器
6. 连接失败：重新进入配网模式
```

## 🔄 故障恢复

### 配网失败处理
- 3分钟超时后进入深度睡眠
- 下次启动时重启计数器仍然有效
- 可以继续尝试重配网

### 完全重置方案
```cpp
// 代码中强制重配网
reconfigManager.forceReconfig();

// 或清除所有WiFi凭据
connectivity.clearStoredCredentials();
```

这个方案提供了灵活的网络重配置能力，既支持当前的reset按钮操作，也为未来的硬件扩展预留了接口。
