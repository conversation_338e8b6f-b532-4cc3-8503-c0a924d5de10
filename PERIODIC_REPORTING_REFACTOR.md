# 定期上报重构：从每日上报改为可配置定期上报

## 📋 重构概述

将系统从固定的每日上报改为可配置的定期上报，支持在Config.h中设置上报间隔时间（单位：秒）。

## 🔧 主要更改

### 1. Config.h 新增配置

**新增配置项**:
```cpp
// Periodic Reporting Configuration
#define PERIODIC_REPORT_INTERVAL_SECONDS (8 * 60 * 60)  // 定期上报间隔（秒）
#define DETECTION_INTERVAL_SECONDS 3600                 // 检测间隔（秒）
#define SLEEP_MIN_SECONDS 10                           // 最小睡眠时间
#define SLEEP_MAX_SECONDS 15                           // 最大睡眠时间
```

### 2. 枚举值更新

**ReportReason枚举**:
```cpp
// 之前
REPORT_DAILY = 8,

// 现在
REPORT_PERIODIC = 8,
```

### 3. 数据结构更新

**ReportingState结构**:
```cpp
// 之前
struct ReportingState {
    time_t lastDailyReportTime;
    time_t nextDailyReportTime;
    // ...
};

// 现在
struct ReportingState {
    time_t lastPeriodicReportTime;
    time_t nextPeriodicReportTime;
    // ...
};
```

### 4. 方法名重构

**SensorDataManager方法**:
```cpp
// 之前 → 现在
shouldReportDaily()           → shouldReportPeriodically()
generateNextDailyReportTime() → generateNextPeriodicReportTime()
getNextDailyReportTimestamp() → getNextPeriodicReportTimestamp()
updateReportState(bool isDailyReport) → updateReportState(bool isPeriodicReport)
forceRegenerateNextDailyReport() → forceRegenerateNextPeriodicReport()
```

**ReportingLogic方法**:
```cpp
// 之前 → 现在
isDailyReportDue() → isPeriodicReportDue()
```

### 5. 变量名重构

**私有成员变量**:
```cpp
// 之前 → 现在
uint32_t dailyReports → uint32_t periodicReports
```

### 6. NVS键名更新

**存储键名**:
```cpp
// 之前 → 现在
KEY_LAST_DAILY_REPORT  → KEY_LAST_PERIODIC_REPORT
KEY_NEXT_DAILY_REPORT  → KEY_NEXT_PERIODIC_REPORT
```

### 7. 常量定义更新

**时间间隔常量**:
```cpp
// 之前
static const unsigned long DAILY_REPORT_INTERVAL = 24UL * 60UL * 60UL;

// 现在
static const unsigned long PERIODIC_REPORT_INTERVAL = PERIODIC_REPORT_INTERVAL_SECONDS;
```

### 8. ConnectivityManager参数更新

**方法参数**:
```cpp
// 之前 → 现在
publishSensorData(..., bool isDailyReport, time_t nextDailyReportTime)
→ publishSensorData(..., bool isDailyReport, time_t nextPeriodicReportTime)

publishSensorError(..., time_t nextDailyReportTime)
→ publishSensorError(..., time_t nextPeriodicReportTime)
```

## 📊 MQTT消息格式更新

### JSON字段名保持兼容
```json
{
  "device_id": "fish-tank-sensor-1-xxxx",
  "timestamp": 1752648698,
  "daily_report": true,  // 保持字段名，但语义改为"定期上报"
  "next_daily_report_time": "2025-07-16 22:10:04",     // 保持字段名
  "next_daily_report_timestamp": 1752675004             // 保持字段名
}
```

**注意**: 为了保持与现有监控系统的兼容性，MQTT消息中的字段名保持不变，但语义已改为定期上报。

## 🔧 配置示例

### 不同的上报间隔配置

**每小时上报**:
```cpp
#define PERIODIC_REPORT_INTERVAL_SECONDS (1 * 60 * 60)  // 1小时
```

**每4小时上报**:
```cpp
#define PERIODIC_REPORT_INTERVAL_SECONDS (4 * 60 * 60)  // 4小时
```

**每8小时上报**:
```cpp
#define PERIODIC_REPORT_INTERVAL_SECONDS (8 * 60 * 60)  // 8小时
```

**每24小时上报（原每日上报）**:
```cpp
#define PERIODIC_REPORT_INTERVAL_SECONDS (24 * 60 * 60)  // 24小时
```

## 🎯 功能特性

### 1. **随机化时间**
- 定期上报时间在配置间隔内随机生成
- 避免多设备同时上报造成服务器压力

### 2. **灵活配置**
- 通过Config.h轻松调整上报间隔
- 支持从分钟到天的任意间隔

### 3. **向后兼容**
- MQTT消息格式保持兼容
- 现有监控系统无需修改

### 4. **调试友好**
- 详细的时间计算日志
- 清晰的下次上报时间显示

## 🔍 调试输出示例

### 定期上报时间生成
```
Current time: 1752648698, offset: 26306 seconds
Next periodic report scheduled at 1752675004, in 7 hours and 18 minutes
Stored value: 1752675004
```

### 定期上报触发
```
Periodic report time reached
Reporting needed: Periodic report due
Periodic report completed. Next periodic report at: 1752704310
```

## ⚠️ 注意事项

### 1. **NVS兼容性**
- 新的NVS键名与旧版本不兼容
- 首次升级时会重新生成定期上报时间

### 2. **时间同步**
- 依赖NTP时间同步的准确性
- 时间不同步时使用系统启动时间作为fallback

### 3. **配置验证**
- 确保`PERIODIC_REPORT_INTERVAL_SECONDS`大于`DETECTION_INTERVAL_SECONDS`
- 避免设置过短的间隔导致频繁上报

## 📁 相关文件修改

- `src/Config.h`: 新增定期上报配置
- `src/ReportingLogic.h`: 更新枚举和方法声明
- `src/SensorDataManager.h`: 更新结构体和方法声明
- `src/SensorDataManager.cpp`: 实现定期上报逻辑
- `src/ConnectivityManager.h`: 更新方法参数
- `src/ConnectivityManager.cpp`: 更新参数处理
- `src/main.cpp`: 更新睡眠时间配置
- 新增: `PERIODIC_REPORTING_REFACTOR.md` (本文档)

这个重构使系统更加灵活，支持根据实际需求调整上报频率，同时保持了与现有系统的兼容性。
