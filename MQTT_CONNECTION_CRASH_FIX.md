# MQTT连接崩溃问题修复

## 🔍 问题分析

用户反映在"Connecting to MQTT broker:"之后串口断开并重连，这表明程序在MQTT连接过程中崩溃或触发看门狗重置。

### 发现的问题：

### 1. **格式化字符串缺少换行符**
**位置**: `src/ConnectivityManager.cpp` 第179行
**问题**: `DEBUG_PRINTF`缺少`\n`，可能导致输出缓冲区问题

### 2. **MQTT缓冲区大小未设置**
**问题**: PubSubClient默认缓冲区可能太小，导致内存问题

### 3. **缺少看门狗喂狗**
**问题**: MQTT连接过程中没有调用`yield()`，可能触发看门狗重置

### 4. **错误处理不足**
**问题**: MQTT连接失败时缺少详细的错误信息和状态检查

### 5. **无限重试风险**
**问题**: 没有限制MQTT连接尝试次数，可能导致无限循环

## ✅ 修复方案

### 1. 修复格式化字符串
```cpp
// 添加缺少的换行符
DEBUG_PRINTF("Connecting to MQTT broker: %s:%d\n", mqttServer.c_str(), mqttPort);
```

### 2. 设置MQTT缓冲区大小
```cpp
// 设置更大的缓冲区以处理较大的消息
mqttClient.setBufferSize(1024);
DEBUG_PRINTF("MQTT buffer size set to: %d bytes\n", mqttClient.getBufferSize());
```

### 3. 添加看门狗喂狗
```cpp
// 在关键位置添加yield()调用
yield(); // 连接前
yield(); // 每次尝试前
yield(); // 延时前后
```

### 4. 增强错误处理和调试
```cpp
// 详细的连接状态检查
DEBUG_PRINTF("Free heap before MQTT connection: %u bytes\n", ESP.getFreeHeap());
DEBUG_PRINTF("WiFi status: %d, IP: %s\n", WiFi.status(), WiFi.localIP().toString().c_str());

// 详细的错误状态报告
int state = mqttClient.state();
switch (state) {
    case -4: DEBUG_PRINTLN("Connection timeout"); break;
    case -3: DEBUG_PRINTLN("Connection lost"); break;
    case -2: DEBUG_PRINTLN("Connect failed"); break;
    case -1: DEBUG_PRINTLN("Disconnected"); break;
    case 1: DEBUG_PRINTLN("Bad protocol"); break;
    case 2: DEBUG_PRINTLN("Bad client ID"); break;
    case 3: DEBUG_PRINTLN("Unavailable"); break;
    case 4: DEBUG_PRINTLN("Bad credentials"); break;
    case 5: DEBUG_PRINTLN("Unauthorized"); break;
    default: DEBUG_PRINTF("Unknown error: %d\n", state); break;
}
```

### 5. 限制重试次数
```cpp
const int maxAttempts = 5; // 最多尝试5次
while (!mqttClient.connected() && (millis() - startTime) < timeoutMs && attemptCount < maxAttempts)
```

### 6. 连接失败后的清理
```cpp
// 清理部分连接状态
mqttClient.disconnect();
mqttConnected = false;

// 详细的失败报告
DEBUG_PRINTF("MQTT connection failed after %d attempts!\n", attemptCount);
DEBUG_PRINTF("Final MQTT state: %d\n", mqttClient.state());
DEBUG_PRINTF("Time elapsed: %lu ms\n", millis() - startTime);
DEBUG_PRINTF("Free heap after failed connection: %u bytes\n", ESP.getFreeHeap());
```

## 🔧 现在的MQTT连接流程

### 1. 连接前检查
- 验证WiFi连接状态
- 检查可用内存
- 显示网络状态信息

### 2. 连接过程
- 设置合适的缓冲区大小
- 限制最大尝试次数（5次）
- 每次尝试前后调用yield()
- 详细记录每次尝试的结果

### 3. 错误处理
- 显示具体的MQTT错误代码和含义
- 记录连接耗时和内存使用
- 清理失败的连接状态

### 4. 成功连接
- 确认连接状态
- 设置连接标志
- 准备发布消息

## 📋 调试输出示例

### 正常连接
```
Free heap before MQTT connection: 234567 bytes
WiFi status: 3, IP: *************
Connecting to MQTT broker: mqtt.server.com:1883
MQTT connection attempt 1...
Connecting with credentials: fish-tank-sensor-1-xxxx
MQTT connected successfully!
```

### 连接失败
```
Free heap before MQTT connection: 234567 bytes
WiFi status: 3, IP: *************
Connecting to MQTT broker: mqtt.server.com:1883
MQTT connection attempt 1...
Connecting with credentials: fish-tank-sensor-1-xxxx
MQTT connection failed, state: -2
Connect failed
MQTT connection attempt 2...
...
MQTT connection failed after 5 attempts!
Final MQTT state: -2
Time elapsed: 5000 ms
Free heap after failed connection: 234500 bytes
```

## ⚠️ 注意事项

1. **内存监控**: 观察连接前后的内存使用情况
2. **网络状态**: 确保WiFi连接稳定
3. **MQTT服务器**: 验证MQTT服务器地址和端口正确
4. **凭据检查**: 确认MQTT用户名和密码正确
5. **防火墙**: 检查网络防火墙是否阻止MQTT连接

## 📁 相关文件修改

- `src/ConnectivityManager.cpp`: 增强MQTT连接错误处理和调试
- 新增: `MQTT_CONNECTION_CRASH_FIX.md` (本文档)

这些修复应该解决MQTT连接时的崩溃问题，并提供详细的调试信息来诊断任何剩余的问题。
