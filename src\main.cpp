#include <Arduino.h>
#include "Config.h"
#include "TDS_Sensor_UART.h"
#include "SensorDataManager.h"
#include "PowerManager.h"
#include "ConnectivityManager.h"
#include "ReportingLogic.h"
#include "NetworkReconfigManager.h"

// Hardware configuration
HardwareSerial &SensorSerial = Serial1;
TDS_Sensor_UART tds_sensor(SensorSerial);

// System components
SensorDataManager dataManager;
PowerManager powerManager;
ConnectivityManager connectivity;
ReportingLogic reporting(dataManager, connectivity);
NetworkReconfigManager reconfigManager;

// Global state
bool systemInitialized = false;
unsigned long lastSensorRead = 0;

// Function declarations
void performSensorCycle();
SensorReading readSensorData();
void printSystemStatus(const SensorReading &reading);
void enterErrorSleep();
void printMemoryInfo();
void printSystemInfo();
bool handleWiFiConnection();
void enterPermanentDeepSleep();
void printReconfigStatus();

void setup()
{
  // Initialize serial communication
  DEBUG_BEGIN(SERIAL_BAUDRATE);
  DEBUG_WAIT(); // Wait up to 5 seconds for serial

  DEBUG_PRINTLN("\n=== Fish Tank Sensor System ===");
  DEBUG_PRINTF("Firmware Version: %s\n", FIRMWARE_VERSION);
  DEBUG_PRINTF("Device: %s\n", DEVICE_NAME);

  // Initialize power manager first to check wake-up reason
  powerManager.begin();

  // Initialize network reconfiguration manager
  reconfigManager.begin();

  // Check if network reconfiguration should be triggered
  if (reconfigManager.shouldTriggerReconfig()) {
    DEBUG_PRINTLN("=== NETWORK RECONFIGURATION TRIGGERED ===");

    // Clear WiFi credentials and force BluFi provisioning
    // connectivity.begin(MQTT_SERVER, MQTT_PORT);
    connectivity.disconnect();
    connectivity.clearStoredCredentials();

    DEBUG_PRINTLN("Starting BluFi provisioning for reconfiguration...");
    if (connectivity.startBluFiProvisioning(180000)) { // 3 minutes timeout
      DEBUG_PRINTLN("Network reconfiguration completed successfully");
      reconfigManager.clearResetCounter();
    } else {
      DEBUG_PRINTLN("Network reconfiguration failed");
    }

    // Continue with normal initialization
  }

  // Initialize sensor data manager
  dataManager.setPowerManager(&powerManager);
  if (!dataManager.begin())
  {
    DEBUG_PRINTLN("Failed to initialize data manager!");
    enterErrorSleep();
    return;
  }

  // Initialize sensor
  tds_sensor.begin(SENSOR_BAUDRATE, SERIAL_8N1, SENSOR_RX_PIN, SENSOR_TX_PIN);
  DEBUG_PRINTF("Sensor initialized on RX=%d, TX=%d, Baud=%ld\n",
               SENSOR_RX_PIN, SENSOR_TX_PIN, SENSOR_BAUDRATE);

  // Initialize connectivity (BluFi mode)
  connectivity.begin(MQTT_SERVER, MQTT_PORT);
  connectivity.setPowerManager(&powerManager);

  // Initialize reporting logic
  reporting.begin();

  systemInitialized = true;

  DEBUG_PRINTF("System initialized. Boot count: %u\n", dataManager.getBootCount());

  // Print reconfiguration status
  printReconfigStatus();

  if (powerManager.isFirstBoot())
  {
    DEBUG_PRINTLN("First boot detected");
    printSystemInfo();

    // Handle WiFi connection for first boot
    if (!handleWiFiConnection())
    {
      DEBUG_PRINTLN("WiFi connection failed on first boot, entering permanent deep sleep");
      enterPermanentDeepSleep();
      return;
    }
  }
  else
  {
    DEBUG_PRINTLN("Wake-up from deep sleep");

    // Handle WiFi connection for wake-up
    if (!handleWiFiConnection())
    {
      DEBUG_PRINTLN("WiFi connection failed after wake-up");
      if (connectivity.getWiFiFailureCount() >= 3)
      {
        DEBUG_PRINTLN("Maximum WiFi failures reached, entering permanent deep sleep");
        enterPermanentDeepSleep();
        return;
      }
    }
  }

  printMemoryInfo();
}

void loop()
{
  if (!systemInitialized)
  {
    enterErrorSleep();
    return;
  }

  // Main sensor reading and reporting cycle
  performSensorCycle();

  // Enter deep sleep until next reading
  DEBUG_PRINTLN("=== END OF CYCLE ===");
  powerManager.enterDeepSleep(DETECTION_INTERVAL_SECONDS);
}

void performSensorCycle()
{
  DEBUG_PRINTLN("\n--- Starting Sensor Cycle ---");

  // Allow sensor to warm up
  while (millis() < SENSOR_WARMUP_DELAY_MS)
  {
    DEBUG_PRINTF("Sensor warming up, wait (%d/%d)...\n", millis(), SENSOR_WARMUP_DELAY_MS);
    delay(SENSOR_WARMUP_DELAY_MS / 3);
  }

  // Read sensor data
  SensorReading currentReading = readSensorData();

  DEBUG_PRINTLN("Analyzing reading for reporting decision...");
  ReportDecision decision = reporting.analyzeReading(currentReading);

  if (!currentReading.valid)
  {
    DEBUG_PRINTLN("Failed to read sensor data, but will report the error");
    // Continue with reporting logic to send error status via MQTT
  }

  // Store reading (even if invalid, for error tracking)
  DEBUG_PRINTLN("Storing reading to data manager...");
  dataManager.storeReading(currentReading);
  if (currentReading.valid)
  {
    DEBUG_PRINTLN("Valid reading stored successfully");
  }
  else
  {
    DEBUG_PRINTLN("Error reading stored for tracking");
  }

  if (decision.shouldReport)
  {
    DEBUG_PRINTF("Reporting needed: %s\n", decision.reasonText.c_str());

    // Execute the report
    bool reportSuccess = reporting.executeReport(currentReading, decision);

    if (reportSuccess)
    {
      DEBUG_PRINTLN("Report sent successfully");
    }
    else
    {
      DEBUG_PRINTLN("Report failed");
    }

    // Disconnect to save power
    connectivity.disconnect();
  }
  else
  {
    DEBUG_PRINTLN("No reporting needed this cycle");
  }

  // Print current status
  printSystemStatus(currentReading);
}

SensorReading readSensorData()
{
  SensorReading reading;

  DEBUG_PRINTLN("Reading sensor data...");

  // Read TDS and temperature
  TDS_Sensor_UART::TDS_Data data = tds_sensor.read_tds_and_temp();

  if (data.valid)
  {
    reading.tds = data.tds;
    reading.temperature = data.temp;

    // Try to get Beijing timestamp, fallback to millis if failed
    DEBUG_PRINTLN("Getting timestamp...");
    reading.timestamp = connectivity.getBeijingTimestamp();
    reading.valid = true;

    DEBUG_PRINTF("Sensor reading: TDS=%d ppm, Temp=%.2f°C\n",
                 reading.tds, reading.temperature);
    DEBUG_PRINTF("Timestamp: %lu\n", reading.timestamp);

    // Try to get time string separately to avoid potential issues
    String timeStr = connectivity.getBeijingTimeString();
    DEBUG_PRINTF("Beijing time: %s\n", timeStr.c_str());
  }
  else
  {
    DEBUG_PRINTLN("Sensor reading failed - no valid data received");
    reading.valid = false;
    reading.timestamp = connectivity.getBeijingTimestamp(); // Still set timestamp for error tracking
    reading.tds = 0;
    reading.temperature = 0.0;
  }

  return reading;
}

void printSystemStatus(const SensorReading &reading)
{
  DEBUG_PRINTLN("\n--- System Status ---");
  DEBUG_PRINTF("Current reading: TDS=%d ppm, Temp=%.2f°C\n",
               reading.tds, reading.temperature);
  DEBUG_PRINTF("Beijing time: %s (timestamp: %lu)\n",
               connectivity.getBeijingTimeString().c_str(), reading.timestamp);

  SensorReading lastReading = dataManager.getLastReading();
  if (lastReading.valid)
  {
    DEBUG_PRINTF("Previous reading: TDS=%d ppm, Temp=%.2f°C (timestamp: %lu)\n",
                 lastReading.tds, lastReading.temperature, lastReading.timestamp);
  }

  DEBUG_PRINTF("Free heap: %u bytes\n", ESP.getFreeHeap());
  DEBUG_PRINTF("Current session uptime: %.2f seconds\n", millis() / 1000.0);
  DEBUG_PRINTF("Total uptime (including deep sleep): %llu seconds\n", powerManager.getTotalUptime());

  reporting.printReportingStats();
  DEBUG_PRINTLN("--------------------");
}

void enterErrorSleep()
{
  DEBUG_PRINTLN("Entering error sleep mode...");
  DEBUG_FLASH();

  // Sleep for minimum time on error
  powerManager.enterDeepSleep(DETECTION_INTERVAL_SECONDS);
}

// Debug helper functions
void printMemoryInfo()
{
  if (ENABLE_MEMORY_MONITORING)
  {
    DEBUG_PRINTF("Free heap: %u bytes\n", ESP.getFreeHeap());
    DEBUG_PRINTF("Largest free block: %u bytes\n", ESP.getMaxAllocHeap());
    DEBUG_PRINTF("Min free heap: %u bytes\n", ESP.getMinFreeHeap());
  }
}

void printSystemInfo()
{
  DEBUG_PRINTLN("\n=== System Information ===");
  DEBUG_PRINTF("Chip model: %s\n", ESP.getChipModel());
  DEBUG_PRINTF("Chip revision: %d\n", ESP.getChipRevision());
  DEBUG_PRINTF("CPU frequency: %d MHz\n", ESP.getCpuFreqMHz());
  DEBUG_PRINTF("Flash size: %d bytes\n", ESP.getFlashChipSize());
  DEBUG_PRINTF("SDK version: %s\n", ESP.getSdkVersion());
  printMemoryInfo();
  DEBUG_PRINTLN("==========================");
}

bool handleWiFiConnection()
{
  DEBUG_PRINTLN("\n--- WiFi Connection Process ---");

  // Print wake-up reason for debugging
  esp_sleep_wakeup_cause_t wakeupReason = esp_sleep_get_wakeup_cause();
  DEBUG_PRINTF("Wake-up reason: %d\n", wakeupReason);
  DEBUG_PRINTF("Is first boot: %s\n", powerManager.isFirstBoot() ? "YES" : "NO");

  // Check if we have stored WiFi credentials
  bool hasCredentials = connectivity.hasStoredCredentials();
  DEBUG_PRINTF("Has stored credentials: %s\n", hasCredentials ? "YES" : "NO");

  if (!hasCredentials)
  {
    DEBUG_PRINTLN("No stored WiFi credentials found");

    // Start BluFi provisioning (3 minutes timeout)
    DEBUG_PRINTLN("Starting BluFi provisioning...");
    if (!connectivity.startBluFiProvisioning(180000))
    { // 3 minutes
      DEBUG_PRINTLN("BluFi provisioning failed or timed out");
      return false;
    }

    DEBUG_PRINTLN("BluFi provisioning completed successfully");
    reconfigManager.clearResetCounter(); // Clear reset counter on successful provisioning
    return true;
  }
  else
  {
    DEBUG_PRINTLN("Found stored WiFi credentials, attempting connection...");

    // Try to connect using stored credentials
    if (connectivity.connectWiFi(30000))
    { // 30 seconds timeout
      DEBUG_PRINTLN("WiFi connection successful");
      reconfigManager.clearResetCounter(); // Clear reset counter on successful connection
      return true;
    }
    else
    {
      DEBUG_PRINTLN("WiFi connection failed with stored credentials");
      return false;
    }
  }
}

void enterPermanentDeepSleep()
{
  DEBUG_PRINTLN("\n=== ENTERING PERMANENT DEEP SLEEP ===");
  DEBUG_PRINTLN("Device will not wake up automatically");
  DEBUG_PRINTLN("Reset the device to restart");
  DEBUG_FLASH();

  // Disconnect everything
  connectivity.disconnect();

  // Clear any wake-up sources
  esp_sleep_disable_wakeup_source(ESP_SLEEP_WAKEUP_ALL);

  // Enter deep sleep without any wake-up timer
  esp_deep_sleep_start();
}

void printReconfigStatus() {
  DEBUG_PRINTLN("\n--- Network Reconfiguration Status ---");
  DEBUG_PRINTF("Reset count: %d\n", reconfigManager.getResetCount());
  DEBUG_PRINTF("Last reset time: %u seconds\n", reconfigManager.getLastResetTime());
  DEBUG_PRINTF("Reset threshold: %d\n", RESET_COUNT_THRESHOLD);
  DEBUG_PRINTF("Time window: %d seconds\n", RESET_TIME_WINDOW_SECONDS);

  if (RECONFIG_BUTTON_PIN >= 0) {
    DEBUG_PRINTF("Hardware button: GPIO %d\n", RECONFIG_BUTTON_PIN);
  } else {
    DEBUG_PRINTLN("Hardware button: Not configured");
  }

  if (RECONFIG_LED_PIN >= 0) {
    DEBUG_PRINTF("Status LED: GPIO %d\n", RECONFIG_LED_PIN);
  } else {
    DEBUG_PRINTLN("Status LED: Not configured");
  }

  DEBUG_PRINTLN("Reconfiguration method: Consecutive reset detection");
  DEBUG_PRINTF("To trigger reconfiguration: Reset device %d times within %d seconds\n",
               RESET_COUNT_THRESHOLD, RESET_TIME_WINDOW_SECONDS);
  DEBUG_PRINTLN("--------------------------------------");
}