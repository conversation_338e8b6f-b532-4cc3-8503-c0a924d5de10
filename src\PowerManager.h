#ifndef POWER_MANAGER_H
#define POWER_MANAGER_H

#include <Arduino.h>
#include <esp_sleep.h>
#include <esp_random.h>

class PowerManager {
public:
    PowerManager();

    // Initialize power management
    void begin();

    // Enter deep sleep for a random interval between min and max seconds
    void enterDeepSleep(uint32_t minSeconds = 300, uint32_t maxSeconds = 600);

    // Get the wake-up reason
    esp_sleep_wakeup_cause_t getWakeupReason();

    // Print wake-up reason for debugging
    void printWakeupReason();

    // Calculate next sleep duration in microseconds
    uint64_t calculateSleepDuration(uint32_t minSeconds, uint32_t maxSeconds);

    // Check if this is the first boot (not a wake-up from deep sleep)
    bool isFirstBoot();

    // Prepare for deep sleep (cleanup, save state, etc.)
    void prepareForSleep();

    // Get total uptime in seconds (including deep sleep periods)
    uint64_t getTotalUptime();

private:
    esp_sleep_wakeup_cause_t wakeupReason;
    bool initialized;

    // Convert seconds to microseconds for ESP32 sleep timer
    static const uint64_t SECOND_TO_MICROSECONDS = 1000000ULL;

    // Update total uptime based on sleep duration
    void updateTotalUptime(uint64_t sleepDurationUs);
};

// RTC memory variables (preserved during deep sleep)
extern RTC_DATA_ATTR uint64_t g_totalUptimeSeconds;
extern RTC_DATA_ATTR uint64_t g_lastSleepTimeMs; // Keep in ms for millis() compatibility
extern RTC_DATA_ATTR uint64_t g_lastSleepDurationUs; // Keep in us for ESP32 API compatibility

#endif // POWER_MANAGER_H
