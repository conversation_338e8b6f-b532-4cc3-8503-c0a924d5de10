#include "SensorDataManager.h"
#include "PowerManager.h"
#include <esp_random.h>

// Static constants
const char* SensorDataManager::NVS_NAMESPACE = "sensor_data";
const char* SensorDataManager::KEY_LAST_TDS = "last_tds";
const char* SensorDataManager::KEY_LAST_TEMP = "last_temp";
const char* SensorDataManager::KEY_LAST_TIMESTAMP = "last_ts";
const char* SensorDataManager::KEY_LAST_REPORT_TIME = "last_report";
const char* SensorDataManager::KEY_LAST_DAILY_REPORT = "last_daily";
const char* SensorDataManager::KEY_NEXT_DAILY_REPORT = "next_daily";
const char* SensorDataManager::KEY_BOOT_COUNT = "boot_count";
const float SensorDataManager::TEMP_THRESHOLD = TEMP_THRESHOLD_C; // °C from Config.h

SensorDataManager::SensorDataManager() : initialized(false), powerManager(nullptr) {
}

SensorDataManager::~SensorDataManager() {
    if (initialized) {
        preferences.end();
    }
}

void SensorDataManager::setPowerManager(PowerManager* pm) {
    powerManager = pm;
}

bool SensorDataManager::begin() {
    if (!preferences.begin(NVS_NAMESPACE, false)) {
        DEBUG_PRINTLN("Failed to initialize NVS");
        return false;
    }
    
    initialized = true;
    loadState();
    
    // Increment boot count
    incrementBootCount();
    
    DEBUG_PRINTF("SensorDataManager initialized. Boot count: %u\n", state.bootCount);
    
    return true;
}

void SensorDataManager::loadState() {
    state.lastReading.tds = preferences.getInt(KEY_LAST_TDS, 0);
    state.lastReading.temperature = preferences.getFloat(KEY_LAST_TEMP, 0.0);
    state.lastReading.timestamp = preferences.getULong64(KEY_LAST_TIMESTAMP, 0);
    state.lastReading.valid = (state.lastReading.timestamp > 0);
    
    state.lastReportTime = preferences.getULong64(KEY_LAST_REPORT_TIME, 0);
    state.lastDailyReportTime = preferences.getULong64(KEY_LAST_DAILY_REPORT, 0);
    state.nextDailyReportTime = preferences.getULong64(KEY_NEXT_DAILY_REPORT, 0);
    state.bootCount = preferences.getUInt(KEY_BOOT_COUNT, 0);
    
    // If no next daily report time is set, generate one
    if (state.nextDailyReportTime == 0) {
        generateNextDailyReportTime();
    }
}

void SensorDataManager::saveState() {
    if (!initialized) return;
    
    preferences.putInt(KEY_LAST_TDS, state.lastReading.tds);
    preferences.putFloat(KEY_LAST_TEMP, state.lastReading.temperature);
    preferences.putULong64(KEY_LAST_TIMESTAMP, state.lastReading.timestamp);
    preferences.putULong64(KEY_LAST_REPORT_TIME, state.lastReportTime);
    preferences.putULong64(KEY_LAST_DAILY_REPORT, state.lastDailyReportTime);
    preferences.putULong64(KEY_NEXT_DAILY_REPORT, state.nextDailyReportTime);
    preferences.putUInt(KEY_BOOT_COUNT, state.bootCount);
}

void SensorDataManager::storeReading(const SensorReading& reading) {
    if (!reading.valid) return;
    
    state.lastReading = reading;
    saveState();
    
    DEBUG_PRINTF("Stored reading: TDS=%d ppm, Temp=%.2f°C, Time=%lu\n", 
                  reading.tds, reading.temperature, reading.timestamp);
}

SensorReading SensorDataManager::getLastReading() {
    return state.lastReading;
}

bool SensorDataManager::shouldReportByThreshold(const SensorReading& currentReading) {
    if (!currentReading.valid || !state.lastReading.valid) {
        return true; // Report first reading
    }
    
    int tdsDiff = abs(currentReading.tds - state.lastReading.tds);
    float tempDiff = abs(currentReading.temperature - state.lastReading.temperature);
    
    bool shouldReport = (tdsDiff >= TDS_THRESHOLD) || (tempDiff >= TEMP_THRESHOLD);
    
    if (shouldReport) {
        DEBUG_PRINTF("Threshold exceeded - TDS diff: %d ppm (threshold: %d), Temp diff: %.2f°C (threshold: %.2f)\n",
                      tdsDiff, TDS_THRESHOLD, tempDiff, TEMP_THRESHOLD);
    }
    
    return shouldReport;
}

bool SensorDataManager::shouldReportDaily() {
    uint64_t currentTime = getCurrentTimestamp();

    // Check if it's time for daily report
    if (currentTime >= state.nextDailyReportTime) {
        DEBUG_PRINTLN("Daily report time reached");
        return true;
    }

    return false;
}

void SensorDataManager::updateReportState(bool isDailyReport) {
    uint64_t currentTime = getCurrentTimestamp();
    state.lastReportTime = currentTime;

    if (isDailyReport) {
        state.lastDailyReportTime = currentTime;
        generateNextDailyReportTime();
        DEBUG_PRINTF("Daily report completed. Next daily report at: %lu\n", state.nextDailyReportTime);
    }

    saveState();
}

ReportingState SensorDataManager::getReportingState() {
    return state;
}

void SensorDataManager::generateNextDailyReportTime() {
    uint64_t currentTime = getCurrentTimestamp();

    // Generate random time within next 24 hours (in milliseconds)
    uint32_t randomOffset = esp_random() % DAILY_REPORT_INTERVAL;
    state.nextDailyReportTime = currentTime + randomOffset;

    // Convert to hours and minutes for logging
    uint32_t hoursFromNow = randomOffset / (60UL * 60UL * 1000UL);
    uint32_t minutesFromNow = (randomOffset % (60UL * 60UL * 1000UL)) / (60UL * 1000UL);

    DEBUG_PRINTF("Next daily report scheduled in %u hours and %u minutes\n", hoursFromNow, minutesFromNow);
}

uint32_t SensorDataManager::getBootCount() {
    return state.bootCount;
}

uint64_t SensorDataManager::getCurrentTimestamp() {
    if (powerManager) {
        return powerManager->getTotalUptime();
    } else {
        // Fallback to millis() if power manager is not set
        return millis();
    }
}

void SensorDataManager::incrementBootCount() {
    state.bootCount++;
    if (initialized) {
        preferences.putUInt(KEY_BOOT_COUNT, state.bootCount);
    }
}

void SensorDataManager::clearAllData() {
    if (!initialized) return;
    
    preferences.clear();
    state = ReportingState();
    DEBUG_PRINTLN("All sensor data cleared");
}
