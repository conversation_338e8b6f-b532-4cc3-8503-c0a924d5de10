#ifndef NETWORK_RECONFIG_MANAGER_H
#define NETWORK_RECONFIG_MANAGER_H

#include <Arduino.h>
#include <Preferences.h>
#include "Config.h"

class NetworkReconfigManager {
public:
    NetworkReconfigManager();
    ~NetworkReconfigManager();
    
    // Initialize the reconfiguration manager
    void begin();
    
    // Check if network reconfiguration should be triggered
    bool shouldTriggerReconfig();
    
    // Record a system reset/boot
    void recordReset();
    
    // Clear reset counter (call after successful network connection)
    void clearResetCounter();
    
    // Force trigger reconfiguration
    void forceReconfig();
    
    // Check button state (for future hardware button)
    bool checkButtonPress();
    
    // Update LED status (for future hardware LED)
    void updateLED(bool blinking = false);
    
    // Get reset count for debugging
    uint8_t getResetCount();
    
    // Get last reset time for debugging
    uint32_t getLastResetTime();

private:
    Preferences preferences;
    bool initialized;
    
    // Reset tracking
    uint8_t resetCount;
    uint32_t lastResetTime;
    uint32_t firstResetTime;
    
    // Hardware pins (future use)
    int buttonPin;
    int ledPin;
    
    // Button state tracking
    bool buttonPressed;
    uint32_t buttonPressStartTime;
    bool ledBlinking;
    uint32_t lastLedToggle;
    bool ledState;
    
    // NVS keys
    static const char* NVS_NAMESPACE;
    static const char* KEY_RESET_COUNT;
    static const char* KEY_LAST_RESET_TIME;
    static const char* KEY_FIRST_RESET_TIME;
    
    // Load state from NVS
    void loadState();
    
    // Save state to NVS
    void saveState();
    
    // Check if resets are within time window
    bool isWithinTimeWindow();
    
    // Initialize hardware pins
    void initializeHardware();
};

#endif // NETWORK_RECONFIG_MANAGER_H
