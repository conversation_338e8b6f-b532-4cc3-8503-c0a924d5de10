# Timestamp Changes - Total Uptime Including Deep Sleep

## Overview
Modified the fish tank sensor system to use total uptime (including deep sleep periods) for timestamps instead of just the current session's `millis()` value.

## Changes Made

### 1. PowerManager.h
- Added RTC_DATA_ATTR macro definition for RTC memory variables
- Added `getTotalUptime()` method to get total uptime including deep sleep
- Added `updateTotalUptime()` private method
- Added extern declarations for RTC memory variables:
  - `g_totalUptimeMs`: Total uptime in milliseconds
  - `g_lastSleepTimeMs`: Last sleep start time
  - `g_lastSleepDurationUs`: Last sleep duration in microseconds

### 2. PowerManager.cpp
- Added RTC memory variable definitions with RTC_DATA_ATTR
- Modified `begin()` method to:
  - Update total uptime after waking from deep sleep
  - Initialize RTC variables on first boot
- Modified `enterDeepSleep()` method to:
  - Store current time and sleep duration before sleeping
  - Update total uptime before entering deep sleep
- Added `getTotalUptime()` implementation:
  - Returns stored total uptime plus current session millis()
- Added `updateTotalUptime()` implementation:
  - Adds sleep duration to total uptime

### 3. SensorDataManager.h
- Added forward declaration for PowerManager class
- Added `setPowerManager()` method
- Added private `powerManager` pointer
- Added private `getCurrentTimestamp()` method

### 4. SensorDataManager.cpp
- Added PowerManager.h include
- Modified constructor to initialize powerManager pointer
- Added `setPowerManager()` implementation
- Modified all time-related methods to use `getCurrentTimestamp()`:
  - `shouldReportDaily()`
  - `updateReportState()`
  - `generateNextDailyReportTime()`
- Added `getCurrentTimestamp()` implementation:
  - Uses PowerManager's getTotalUptime() if available
  - Falls back to millis() if PowerManager not set

### 5. main.cpp
- Modified `readSensorData()` to use `powerManager.getTotalUptime()` for timestamps
- Modified `printSystemStatus()` to show both current session uptime and total uptime
- Added call to `dataManager.setPowerManager(&powerManager)` in setup()

## How It Works

1. **First Boot**: RTC variables are initialized to 0
2. **During Operation**: Current timestamp = stored total uptime + current millis()
3. **Before Deep Sleep**: 
   - Store current total uptime
   - Store sleep duration
4. **After Wake-up**: 
   - Add sleep duration to stored total uptime
   - Continue with updated total

## Benefits

- Timestamps now represent true elapsed time since first boot
- Sensor readings maintain chronological order across deep sleep cycles
- Daily report scheduling works correctly across sleep periods
- Better data consistency for long-term monitoring

## RTC Memory Usage

The system uses ESP32's RTC memory to preserve timing data across deep sleep:
- `g_totalUptimeMs`: 8 bytes
- `g_lastSleepTimeMs`: 8 bytes  
- `g_lastSleepDurationUs`: 8 bytes
- Total: 24 bytes of RTC memory

## Testing

To verify the implementation:
1. Monitor serial output for timestamp values
2. Check that timestamps increase monotonically across sleep cycles
3. Verify that "Total uptime" includes sleep periods
4. Confirm daily reports are scheduled correctly

## Example Output

```
Sensor reading: TDS=450 ppm, Temp=24.5°C, Timestamp=125430 ms
Current session uptime: 5430 ms
Total uptime (including deep sleep): 125430 ms
```

This shows the device has been running for ~2 minutes total, but current session is only ~5 seconds.
