# 网络重配置功能测试指南

## 🧪 测试准备

### 1. 编译和上传代码
```bash
# 确保包含新文件
src/NetworkReconfigManager.h
src/NetworkReconfigManager.cpp

# 编译上传到设备
```

### 2. 串口监控设置
```
波特率: 115200
监控工具: Arduino IDE串口监视器 / PlatformIO Monitor
```

## 🔬 测试步骤

### 测试1: 正常启动检查
```
1. 上传代码并重启设备
2. 观察串口输出，应该看到：
   - "NetworkReconfigManager initialized. Reset count: 1"
   - "--- Network Reconfiguration Status ---"
   - "Reset count: 1"
   - "Reset threshold: 3"
   - "Time window: 30 seconds"
```

### 测试2: 重启计数功能
```
1. 快速按reset按钮（第2次重启）
2. 观察输出：
   - "Reset count incremented to: 2"
   
3. 再次快速按reset按钮（第3次重启）
4. 观察输出：
   - "Reset count incremented to: 3"
   - "Reconfiguration triggered by 3 resets within 30 seconds"
   - "=== NETWORK RECONFIGURATION TRIGGERED ==="
```

### 测试3: 重配网触发
```
当看到重配网触发后，应该观察到：
1. "Starting BluFi provisioning for reconfiguration..."
2. 设备进入BluFi配网模式
3. 可以用手机ESP BluFi App连接设备
4. 配网成功后："Network reconfiguration completed successfully"
```

### 测试4: 时间窗口测试
```
1. 重启设备1次
2. 等待35秒（超过30秒时间窗口）
3. 再重启2次
4. 应该看到："Starting new reset sequence"
5. 重启计数从1重新开始，不会触发重配网
```

### 测试5: 自动清除功能
```
1. 设备正常连接WiFi后
2. 观察输出："WiFi connection successful"
3. 下次重启时重启计数应该从1开始
4. 验证重启计数器已被清除
```

## 📊 预期输出示例

### 正常启动
```
=== Fish Tank Sensor System ===
Firmware Version: 1.0.0
Device: FishTankSensor
NetworkReconfigManager initialized. Reset count: 1
Starting new reset sequence

--- Network Reconfiguration Status ---
Reset count: 1
Last reset time: 12345 seconds
Reset threshold: 3
Time window: 30 seconds
Hardware button: Not configured
Status LED: Not configured
Reconfiguration method: Consecutive reset detection
To trigger reconfiguration: Reset device 3 times within 30 seconds
--------------------------------------
```

### 触发重配网
```
NetworkReconfigManager initialized. Reset count: 3
Reset count incremented to: 3
Time since first reset: 15 seconds, within window: yes
Reconfiguration triggered by 3 resets within 30 seconds
=== NETWORK RECONFIGURATION TRIGGERED ===
Starting BluFi provisioning for reconfiguration...
BluFi provisioning completed successfully
Network reconfiguration completed successfully
Clearing reset counter after successful connection
```

### WiFi连接成功
```
Found stored WiFi credentials, attempting connection...
WiFi connection successful
Clearing reset counter after successful connection
```

## 🐛 故障排除

### 问题1: 重启计数不工作
**症状**: 每次重启都显示"Reset count: 1"
**原因**: NVS存储问题
**解决**: 检查NVS初始化，确保preferences.begin()成功

### 问题2: 时间窗口不准确
**症状**: 时间窗口计算错误
**原因**: millis()溢出或时间计算错误
**解决**: 检查时间计算逻辑，使用绝对时间戳

### 问题3: 重配网不触发
**症状**: 达到3次重启但不触发重配网
**原因**: 时间窗口超时或条件判断错误
**解决**: 检查isWithinTimeWindow()函数逻辑

### 问题4: BluFi配网失败
**症状**: 重配网触发但BluFi启动失败
**原因**: WiFi凭据清除失败或BluFi初始化问题
**解决**: 检查clearStoredCredentials()和startBluFiProvisioning()

## 🔧 调试技巧

### 1. 增加调试输出
```cpp
// 在NetworkReconfigManager.cpp中添加更多DEBUG_PRINTF
DEBUG_PRINTF("Current time: %u, first reset: %u, diff: %u\n", 
             currentTime, firstResetTime, currentTime - firstResetTime);
```

### 2. 手动触发测试
```cpp
// 在setup()中添加测试代码
reconfigManager.forceReconfig(); // 强制触发重配网
```

### 3. 查看NVS内容
```cpp
// 添加NVS内容打印
DEBUG_PRINTF("NVS reset_count: %d\n", preferences.getUChar("reset_count", 0));
DEBUG_PRINTF("NVS last_reset: %u\n", preferences.getUInt("last_reset", 0));
```

## 📱 手机端测试

### ESP BluFi App使用
```
1. 下载ESP BluFi App (Android/iOS)
2. 确保手机蓝牙已开启
3. 搜索设备："fish-tank-sensor-1-xxxxxx"
4. 连接设备
5. 输入WiFi信息：
   - SSID: 你的WiFi名称
   - Password: WiFi密码
6. 点击"Confirm"
7. 等待配网完成
```

### 配网成功标志
```
设备端：
- "BluFi provisioning completed successfully"
- "Network reconfiguration completed successfully"

手机端：
- 显示"Configuration successful"
- 设备自动断开BluFi连接
```

## ✅ 测试检查清单

- [ ] 正常启动显示重启计数
- [ ] 连续重启计数递增
- [ ] 达到阈值触发重配网
- [ ] BluFi配网模式启动
- [ ] 手机可以连接和配网
- [ ] 配网成功后清除计数器
- [ ] 时间窗口机制正常工作
- [ ] WiFi连接成功后清除计数器
- [ ] 超时重启序列重新开始

## 🎯 性能验证

### 响应时间
- 重启到检测: < 2秒
- 触发到BluFi启动: < 5秒
- 配网完成到连接: < 30秒

### 可靠性
- 连续10次测试成功率: > 95%
- 误触发率: < 1%
- 时间窗口准确性: ±2秒

这个测试指南帮助验证网络重配置功能的各个方面，确保在实际使用中的可靠性。
