#include "NetworkReconfigManager.h"

// Static constants
const char* NetworkReconfigManager::NVS_NAMESPACE = "net_reconfig";
const char* NetworkReconfigManager::KEY_RESET_COUNT = "reset_count";
const char* NetworkReconfigManager::KEY_LAST_RESET_TIME = "last_reset";
const char* NetworkReconfigManager::KEY_FIRST_RESET_TIME = "first_reset";

NetworkReconfigManager::NetworkReconfigManager() : 
    initialized(false),
    resetCount(0),
    lastResetTime(0),
    firstResetTime(0),
    buttonPin(RECONFIG_BUTTON_PIN),
    ledPin(RECONFIG_LED_PIN),
    buttonPressed(false),
    buttonPressStartTime(0),
    ledBlinking(false),
    lastLedToggle(0),
    ledState(false)
{
}

NetworkReconfigManager::~NetworkReconfigManager() {
    if (initialized) {
        preferences.end();
    }
}

void NetworkReconfigManager::begin() {
    if (!preferences.begin(NVS_NAMESPACE, false)) {
        DEBUG_PRINTLN("Failed to initialize NetworkReconfigManager NVS");
        return;
    }
    
    initialized = true;
    loadState();
    initializeHardware();
    
    DEBUG_PRINTF("NetworkReconfigManager initialized. Reset count: %d\n", resetCount);
    
    // Record this boot
    recordReset();
}

void NetworkReconfigManager::recordReset() {
    if (!initialized) return;
    
    uint32_t currentTime = millis() / 1000; // Convert to seconds
    
    // If this is the first reset or outside time window, start new sequence
    if (resetCount == 0 || !isWithinTimeWindow()) {
        resetCount = 1;
        firstResetTime = currentTime;
        lastResetTime = currentTime;
        DEBUG_PRINTLN("Starting new reset sequence");
    } else {
        // Increment reset count within time window
        resetCount++;
        lastResetTime = currentTime;
        DEBUG_PRINTF("Reset count incremented to: %d\n", resetCount);
    }
    
    saveState();
}

bool NetworkReconfigManager::shouldTriggerReconfig() {
    if (!initialized) return false;
    
    // Check if reset count threshold is reached within time window
    if (resetCount >= RESET_COUNT_THRESHOLD && isWithinTimeWindow()) {
        DEBUG_PRINTF("Reconfiguration triggered by %d resets within %d seconds\n", 
                     resetCount, RESET_TIME_WINDOW_SECONDS);
        return true;
    }
    
    // Check button press (for future hardware)
    if (checkButtonPress()) {
        DEBUG_PRINTLN("Reconfiguration triggered by button press");
        return true;
    }
    
    return false;
}

void NetworkReconfigManager::clearResetCounter() {
    if (!initialized) return;
    
    DEBUG_PRINTLN("Clearing reset counter after successful connection");
    resetCount = 0;
    lastResetTime = 0;
    firstResetTime = 0;
    saveState();
}

void NetworkReconfigManager::forceReconfig() {
    DEBUG_PRINTLN("Force triggering network reconfiguration");
    resetCount = RESET_COUNT_THRESHOLD;
    lastResetTime = millis() / 1000;
    firstResetTime = lastResetTime;
    saveState();
}

bool NetworkReconfigManager::checkButtonPress() {
    if (buttonPin < 0) return false; // No button configured
    
    bool currentButtonState = digitalRead(buttonPin) == LOW; // Assuming active low
    
    if (currentButtonState && !buttonPressed) {
        // Button just pressed
        buttonPressed = true;
        buttonPressStartTime = millis();
        DEBUG_PRINTLN("Button pressed");
    } else if (!currentButtonState && buttonPressed) {
        // Button released
        buttonPressed = false;
        DEBUG_PRINTLN("Button released");
    } else if (currentButtonState && buttonPressed) {
        // Button held down
        uint32_t holdTime = millis() - buttonPressStartTime;
        if (holdTime >= RECONFIG_BUTTON_HOLD_TIME_MS) {
            buttonPressed = false; // Prevent multiple triggers
            updateLED(true); // Start LED blinking
            return true;
        }
    }
    
    return false;
}

void NetworkReconfigManager::updateLED(bool blinking) {
    if (ledPin < 0) return; // No LED configured
    
    if (blinking) {
        ledBlinking = true;
        // Blink LED at 2Hz (500ms on/off)
        if (millis() - lastLedToggle >= 500) {
            ledState = !ledState;
            digitalWrite(ledPin, ledState);
            lastLedToggle = millis();
        }
    } else {
        ledBlinking = false;
        digitalWrite(ledPin, LOW); // Turn off LED
    }
}

uint8_t NetworkReconfigManager::getResetCount() {
    return resetCount;
}

uint32_t NetworkReconfigManager::getLastResetTime() {
    return lastResetTime;
}

void NetworkReconfigManager::loadState() {
    resetCount = preferences.getUChar(KEY_RESET_COUNT, 0);
    lastResetTime = preferences.getUInt(KEY_LAST_RESET_TIME, 0);
    firstResetTime = preferences.getUInt(KEY_FIRST_RESET_TIME, 0);
    
    DEBUG_PRINTF("Loaded state: count=%d, last=%u, first=%u\n", 
                 resetCount, lastResetTime, firstResetTime);
}

void NetworkReconfigManager::saveState() {
    if (!initialized) return;
    
    preferences.putUChar(KEY_RESET_COUNT, resetCount);
    preferences.putUInt(KEY_LAST_RESET_TIME, lastResetTime);
    preferences.putUInt(KEY_FIRST_RESET_TIME, firstResetTime);
    
    DEBUG_PRINTF("Saved state: count=%d, last=%u, first=%u\n", 
                 resetCount, lastResetTime, firstResetTime);
}

bool NetworkReconfigManager::isWithinTimeWindow() {
    if (firstResetTime == 0) return false;
    
    uint32_t currentTime = millis() / 1000;
    uint32_t timeSinceFirst = currentTime - firstResetTime;
    
    bool withinWindow = timeSinceFirst <= RESET_TIME_WINDOW_SECONDS;
    DEBUG_PRINTF("Time since first reset: %u seconds, within window: %s\n", 
                 timeSinceFirst, withinWindow ? "yes" : "no");
    
    return withinWindow;
}

void NetworkReconfigManager::initializeHardware() {
    // Initialize button pin (future use)
    if (buttonPin >= 0) {
        pinMode(buttonPin, INPUT_PULLUP);
        DEBUG_PRINTF("Button pin %d initialized\n", buttonPin);
    }
    
    // Initialize LED pin (future use)
    if (ledPin >= 0) {
        pinMode(ledPin, OUTPUT);
        digitalWrite(ledPin, LOW);
        DEBUG_PRINTF("LED pin %d initialized\n", ledPin);
    }
}
