#ifndef SENSOR_DATA_MANAGER_H
#define SENSOR_DATA_MANAGER_H

#include <Arduino.h>
#include <Preferences.h>
#include "TDS_Sensor_UART.h"
#include "Config.h"

// Forward declaration
class PowerManager;

struct SensorReading {
    int tds;
    float temperature;
    unsigned long timestamp;
    bool valid;
    
    SensorReading() : tds(0), temperature(0.0), timestamp(0), valid(false) {}
    SensorReading(int t, float temp, unsigned long ts) : tds(t), temperature(temp), timestamp(ts), valid(true) {}
};

struct ReportingState {
    unsigned long lastReportTime;
    unsigned long lastDailyReportTime;
    unsigned long nextDailyReportTime;
    SensorReading lastReading;
    uint32_t bootCount;
    
    ReportingState() : lastReportTime(0), lastDailyReportTime(0), nextDailyReportTime(0), bootCount(0) {}
};

class SensorDataManager {
public:
    SensorDataManager();
    ~SensorDataManager();

    // Initialize the data manager
    bool begin();

    // Set power manager reference for total uptime access
    void setPowerManager(PowerManager* pm);
    
    // Store a new sensor reading
    void storeReading(const SensorReading& reading);
    
    // Get the last stored reading
    SensorReading getLastReading();
    
    // Check if we should report based on thresholds
    bool shouldReportByThreshold(const SensorReading& currentReading);
    
    // Check if we should do daily report
    bool shouldReportDaily();
    
    // Update reporting state after successful report
    void updateReportState(bool isDailyReport = false);
    
    // Get reporting state
    ReportingState getReportingState();
    
    // Generate next random daily report time (within next 24 hours)
    void generateNextDailyReportTime();

    // Get next daily report time as Unix timestamp
    time_t getNextDailyReportTimestamp();

    // Get boot count
    uint32_t getBootCount();
    
    // Increment boot count
    void incrementBootCount();
    
    // Clear all stored data (for debugging)
    void clearAllData();

private:
    Preferences preferences;
    ReportingState state;
    bool initialized;
    PowerManager* powerManager;  // Reference to power manager for total uptime

    // NVS keys
    static const char* NVS_NAMESPACE;
    static const char* KEY_LAST_TDS;
    static const char* KEY_LAST_TEMP;
    static const char* KEY_LAST_TIMESTAMP;
    static const char* KEY_LAST_REPORT_TIME;
    static const char* KEY_LAST_DAILY_REPORT;
    static const char* KEY_NEXT_DAILY_REPORT;
    static const char* KEY_BOOT_COUNT;

    // Thresholds (using Config.h values)
    static const int TDS_THRESHOLD = TDS_THRESHOLD_PPM;  // ppm
    static const float TEMP_THRESHOLD;      // °C (defined in .cpp file)
    static const unsigned long DAILY_REPORT_INTERVAL = 24UL * 60UL * 60UL; // 24 hours in seconds

    // Get current timestamp (total uptime)
    uint64_t getCurrentTimestamp();

    // Load state from NVS
    void loadState();

    // Save state to NVS
    void saveState();
};

#endif // SENSOR_DATA_MANAGER_H
