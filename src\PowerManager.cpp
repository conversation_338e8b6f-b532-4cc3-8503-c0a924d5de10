#include "PowerManager.h"
#include "Config.h"

// RTC memory variables (preserved during deep sleep)
RTC_DATA_ATTR uint64_t g_totalUptimeSeconds = 0;
RTC_DATA_ATTR uint64_t g_lastSleepTimeMs = 0; // Keep in ms for millis() compatibility
RTC_DATA_ATTR uint64_t g_lastSleepDurationUs = 0; // Keep in us for ESP32 API compatibility

PowerManager::PowerManager() : initialized(false) {
    wakeupReason = esp_sleep_get_wakeup_cause();
}

void PowerManager::begin() {
    initialized = true;

    // Print wake-up reason for debugging
    printWakeupReason();

    // Update total uptime if waking from deep sleep
    if (!isFirstBoot()) {
        updateTotalUptime(g_lastSleepDurationUs);
        DEBUG_PRINTF("Updated total uptime after sleep: %llu seconds\n", g_totalUptimeSeconds);
    } else {
        // First boot - initialize RTC variables
        g_totalUptimeSeconds = 0;
        g_lastSleepTimeMs = 0;
        g_lastSleepDurationUs = 0;
        DEBUG_PRINTLN("First boot - initialized RTC variables");
    }

    // Configure wake-up source (timer only for now)
    esp_sleep_enable_timer_wakeup(calculateSleepDuration(300, 600));

    DEBUG_PRINTLN("PowerManager initialized");
}

void PowerManager::enterDeepSleep(uint32_t minSeconds, uint32_t maxSeconds) {
    if (!initialized) {
        DEBUG_PRINTLN("PowerManager not initialized!");
        return;
    }

    // Calculate sleep duration
    uint64_t sleepDuration = calculateSleepDuration(minSeconds, maxSeconds);

    // Convert back to seconds for logging
    uint32_t sleepSeconds = sleepDuration / SECOND_TO_MICROSECONDS;

    // Store current time and sleep duration for next wake-up
    g_lastSleepTimeMs = millis();
    g_lastSleepDurationUs = sleepDuration;

    // Update total uptime before sleep
    g_totalUptimeSeconds = getTotalUptime();

    DEBUG_PRINTF("Current uptime: %llu seconds, entering deep sleep for %u seconds...\n",
                 g_totalUptimeSeconds, sleepSeconds);
    Serial.flush(); // Ensure all serial output is sent

    // Prepare for sleep
    prepareForSleep();

    // Configure timer wake-up
    esp_sleep_enable_timer_wakeup(sleepDuration);

    // Enter deep sleep
    esp_deep_sleep_start();
}

esp_sleep_wakeup_cause_t PowerManager::getWakeupReason() {
    return wakeupReason;
}

void PowerManager::printWakeupReason() {
    switch (wakeupReason) {
        case ESP_SLEEP_WAKEUP_EXT0:
            DEBUG_PRINTLN("Wakeup caused by external signal using RTC_IO");
            break;
        case ESP_SLEEP_WAKEUP_EXT1:
            DEBUG_PRINTLN("Wakeup caused by external signal using RTC_CNTL");
            break;
        case ESP_SLEEP_WAKEUP_TIMER:
            DEBUG_PRINTLN("Wakeup caused by timer");
            break;
        case ESP_SLEEP_WAKEUP_TOUCHPAD:
            DEBUG_PRINTLN("Wakeup caused by touchpad");
            break;
        case ESP_SLEEP_WAKEUP_ULP:
            DEBUG_PRINTLN("Wakeup caused by ULP program");
            break;
        default:
            DEBUG_PRINTF("Wakeup was not caused by deep sleep: %d\n", wakeupReason);
            break;
    }
}

uint64_t PowerManager::calculateSleepDuration(uint32_t minSeconds, uint32_t maxSeconds) {
    if (maxSeconds <= minSeconds) {
        maxSeconds = minSeconds + 1;
    }

    // Generate random duration between min and max seconds
    uint32_t rangeMicroseconds = (maxSeconds - minSeconds) * 1000000;
    uint32_t randomOffset = esp_random() % rangeMicroseconds;
    uint64_t sleepDuration = (minSeconds * SECOND_TO_MICROSECONDS) + randomOffset;

    return sleepDuration;
}

bool PowerManager::isFirstBoot() {
    DEBUG_PRINTF("Wakeup reason: %d\n", wakeupReason);
    return (wakeupReason != ESP_SLEEP_WAKEUP_TIMER && 
            wakeupReason != ESP_SLEEP_WAKEUP_EXT0 && 
            wakeupReason != ESP_SLEEP_WAKEUP_EXT1 &&
            wakeupReason != ESP_SLEEP_WAKEUP_TOUCHPAD &&
            wakeupReason != ESP_SLEEP_WAKEUP_ULP);
}

void PowerManager::prepareForSleep() {
    // Flush any remaining serial output
    Serial.flush();

    // Small delay to ensure everything is settled
    delay(100);

    // Additional cleanup can be added here if needed
    // For example: turning off peripherals, saving final state, etc.
}

uint64_t PowerManager::getTotalUptime() {
    // Current uptime is the total stored uptime plus the current session time in seconds
    return g_totalUptimeSeconds + (millis() / 1000);
}

void PowerManager::updateTotalUptime(uint64_t sleepDurationUs) {
    // Add the sleep duration (in seconds) to the total uptime
    g_totalUptimeSeconds += (sleepDurationUs / 1000000); // Convert microseconds to seconds
}
